import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ID, ITimeSlotSession } from '@gauzy/contracts';
import { TimeSlotSession } from './time-slot-session.entity';

@Injectable()
export class TimeSlotSessionService {
	constructor(
		@InjectRepository(TimeSlotSession)
		private readonly timeSlotSessionRepository: Repository<TimeSlotSession>
	) {}

	/**
	 * Create a new TimeSlotSession entry
	 */
	async createSession(
		sessionId: string,
		timeSlotId: ID,
		employeeId: ID,
		tenantId: ID,
		organizationId: ID,
		startTime?: Date,
		lastActivity?: Date
	): Promise<ITimeSlotSession> {
		const session = this.timeSlotSessionRepository.create({
			sessionId,
			timeSlotId,
			employeeId,
			tenantId,
			organizationId,
			startTime,
			lastActivity
		});

		return await this.timeSlotSessionRepository.save(session);
	}

	/**
	 * Find TimeSlots by sessionId
	 */
	async findTimeSlotsBySessionId(
		sessionId: string,
		tenantId: ID,
		organizationId: ID
	): Promise<ITimeSlotSession[]> {
		return await this.timeSlotSessionRepository.find({
			where: {
				sessionId,
				tenantId,
				organizationId
			},
			relations: ['timeSlot', 'employee']
		});
	}

	/**
	 * Update session activity time
	 */
	async updateSessionActivity(
		sessionId: string,
		timeSlotId: ID,
		lastActivity: Date
	): Promise<void> {
		await this.timeSlotSessionRepository.update(
			{ sessionId, timeSlotId },
			{ lastActivity, updatedAt: new Date() }
		);
	}

	/**
	 * Delete sessions for a specific TimeSlot
	 */
	async deleteSessionsByTimeSlot(timeSlotId: ID): Promise<void> {
		await this.timeSlotSessionRepository.delete({ timeSlotId });
	}
}
